"""
单目视觉测量核心类
基于K230开发板的图像处理模块实现目标物测量功能
"""

import time
import math

# K230平台导入
from media.sensor import *
from media.display import *
from media.media import *
import image  # K230的image模块
K230_PLATFORM = True


class MonoMeasure:
    """
    单目视觉测量核心类
    负责A4纸检测、形状识别、距离测量等功能
    """

    def __init__(self, a4_width_cm=20.9, a4_height_cm=29.5):
        """
        初始化测量类

        参数:
            a4_width_cm: A4纸宽度(厘米)，默认21.0cm
            a4_height_cm: A4纸高度(厘米)，默认29.7cm
        """
        # A4纸标准尺寸参数
        self.a4_width_cm = a4_width_cm      # A4纸实际宽度(厘米)，用于像素-厘米转换
        self.a4_height_cm = a4_height_cm    # A4纸实际高度(厘米)，用于距离计算基准

        # 摄像头标定参数
        self.focal_length = None            # 摄像头等效焦距，标定后计算得出
        self.calibrated = False             # 标定状态标志，True表示已完成标定
        self.MIN_FOCAL_LENGTH  = 680        # 最小焦距阈值(像素)，避免除零错误
        self.MAX_FOCAL_LENGTH = 800         # 最大焦距阈值(像素)，避免异常值

        # 测量结果缓存字典
        self.last_measurement = {
            'distance_cm': 0.0,             # 最后测量的距离值(厘米)
            'shape_type': '',               # 最后识别的形状类型(circle/triangle/square)
            'size_cm': 0.0,                 # 最后测量的图形尺寸(厘米)
            'timestamp': 0                  # 测量时间戳(毫秒)
        }

        # A4纸检测算法参数
        self.min_a4_area = 5000            # A4纸最小面积阈值(像素²)，过滤小噪声
        self.max_a4_area = 30000            # A4纸最大面积阈值(像素²)，过滤过大区域

        # 计算有效区域长宽比（减去2cm边框）
        self.effective_width_cm = self.a4_width_cm - 4.0   # 有效宽度：21-4=17cm
        self.effective_height_cm = self.a4_height_cm - 4.0 # 有效高度：29.7-4=25.7cm
        self.a4_aspect_ratio = self.effective_height_cm / self.effective_width_cm  # 有效区域长宽比(约1.51)
        self.aspect_tolerance = 0.3         # 长宽比容差范围，允许的偏差值

        # ==================== 形状识别阈值参数配置区域 ====================
        # 可在此处直接修改所有阈值参数，方便调试和优化识别效果

        # 1. 图形检测基础阈值参数
        self.BLACK_L_MAX = 50                   # LAB色彩空间L通道最大值 (调大=检测更亮的"黑色"，调小=只检测很黑的区域)
        self.BLACK_A_MIN = -128                 # LAB色彩空间A通道最小值 (通常不需要修改)
        self.BLACK_A_MAX = 127                  # LAB色彩空间A通道最大值 (通常不需要修改)
        self.BLACK_B_MIN = -128                 # LAB色彩空间B通道最小值 (通常不需要修改)
        self.BLACK_B_MAX = 127                  # LAB色彩空间B通道最大值 (通常不需要修改)
        self.PIXELS_THRESHOLD = 200             # 最小像素数阈值 (调大=过滤更多小噪声，调小=检测更小的图形)
        self.AREA_THRESHOLD = 500               # 最小面积阈值 (调大=过滤更多小区域，调小=检测更小的图形)
        self.MIN_BLOB_AREA = 1000               # 有效图形最小面积 (调大=只检测大图形，调小=检测小图形)

        # 2. 圆形识别阈值参数
        self.CIRCLE_CIRCULARITY_MIN = 0.80      # 圆形最小圆形度阈值 (调大=更严格的圆形要求，调小=更宽松)
        self.CIRCLE_RECTANGULARITY_MAX = 0.80   # 圆形最大矩形度阈值 (调大=允许更方的圆形，调小=要求更圆)

        # 3. 正方形识别阈值参数
        self.SQUARE_RECTANGULARITY_MIN = 0.95   # 正方形最小矩形度阈值 (调大=更严格的矩形要求，调小=更宽松)
        self.SQUARE_ASPECT_RATIO_MAX = 1.25      # 正方形最大长宽比阈值 (调大=允许更长的矩形，调小=要求更接近正方形)
        self.SQUARE_CIRCULARITY_MIN = 0.60      # 正方形最小圆形度阈值 (调大=要求更圆润的角，调小=允许更尖锐的角)

        # 4. 三角形识别阈值参数
        self.TRIANGLE_CIRCULARITY_MAX = 0.7     # 三角形最大圆形度阈值 (调大=允许更圆润的三角形，调小=要求更尖锐)
        self.TRIANGLE_RECTANGULARITY_MAX = 0.7  # 三角形最大矩形度阈值 (调大=允许更接近矩形的三角形，调小=更严格)

        # 5. 备用判断逻辑阈值参数
        self.FALLBACK_CIRCLE_THRESHOLD = 0.75    # 备用圆形判断阈值 (当无法明确分类时使用)

        # ==================== 阈值参数配置区域结束 ====================

        print("MonoMeasure初始化完成")
        print("A4纸总尺寸: {}cm × {}cm".format(self.a4_width_cm, self.a4_height_cm))
        print("A4纸有效区域: {}cm × {}cm (减去2cm边框)".format(self.effective_width_cm, self.effective_height_cm))
        print("有效区域长宽比: {:.3f}".format(self.a4_aspect_ratio))

    def calibrate(self, img, known_distance_cm):
        """
        标定摄像头焦距

        参数:
            img: 输入图像，包含已知距离的A4纸
            known_distance_cm: A4纸到摄像头的真实距离(厘米)

        返回:
            bool: 标定是否成功
            float: 计算得到的焦距值
        """
        try:
            print("开始标定，已知距离: {}cm".format(known_distance_cm))

            # 检测A4纸边框和尺寸信息
            a4_data = self._detect_a4_paper(img)
            if a4_data is None:
                print("标定失败: 未检测到A4纸")
                return False, 0.0

            # 提取A4纸在图像中的像素高度
            height_px = a4_data['height_px']  # A4纸在图像中的像素高度
            print("检测到A4纸像素高度: {}px".format(height_px))

            # 使用三角相似原理计算摄像头等效焦距
            focal_length = (height_px * known_distance_cm) / self.effective_height_cm

            # 检查焦距是否在有效范围内
            if self.MIN_FOCAL_LENGTH <= focal_length <= self.MAX_FOCAL_LENGTH:
                self.focal_length = focal_length
                self.calibrated = True  # 设置标定完成标志
                print("标定成功，焦距: {:.2f}".format(self.focal_length))
                return True, self.focal_length
            else:
                print("标定失败: 计算的焦距值 {:.2f} 不在有效范围({}-{})内".format(
                    focal_length, self.MIN_FOCAL_LENGTH, self.MAX_FOCAL_LENGTH))
                return False, 0.0

        except Exception as e:
            print("标定异常: {}".format(e))
            return False, 0.0

    def measure(self, img):
        """
        测量目标物距离和尺寸

        参数:
            img: 输入图像

        返回:
            tuple: (distance_cm, shape_type, size_cm) 或 None
        """
        try:
            # 检查标定状态，确保系统已完成焦距标定
            if not self.calibrated or self.focal_length is None:
                print("测量失败: 系统未标定")
                return None

            # 检测A4纸边框和位置信息
            a4_data = self._detect_a4_paper(img)
            if a4_data is None:
                print("测量失败: 未检测到A4纸")
                return None

            # 使用三角相似原理计算目标物距离
            height_px = a4_data['height_px']  # A4纸有效区域在当前图像中的像素高度
            # 距离公式: D = (H_real × F) / H_pixel
            # 注意：使用有效区域高度进行计算
            distance_cm = (self.effective_height_cm * self.focal_length) / height_px

            # 对A4纸进行透视变换矫正，获得标准顶视图
            warped_img = self._correct_perspective(img, a4_data['corners'])
            if warped_img is None:
                print("测量失败: 透视矫正失败")
                return None

            # 在矫正后的图像中识别图形并计算尺寸
            shape_result = self._detect_shape(warped_img)
            if shape_result is None:
                print("测量失败: 未检测到图形")
                return None

            shape_type, size_px = shape_result  # 图形类型和像素尺寸

            # 将像素尺寸转换为实际厘米尺寸
            # 使用有效区域宽度进行转换
            pixel_to_cm_ratio = self.effective_width_cm / a4_data['width_px']  # 像素到厘米的转换比例
            size_cm = size_px * pixel_to_cm_ratio  # 图形的实际尺寸(厘米)

            # 更新最后一次测量结果缓存
            # 获取时间戳（K230平台）
            timestamp = time.ticks_ms()     # K230平台使用ticks_ms()

            self.last_measurement = {
                'distance_cm': distance_cm,     # 测量距离
                'shape_type': shape_type,       # 图形类型
                'size_cm': size_cm,             # 图形尺寸
                'timestamp': timestamp          # 测量时间戳
            }

            print("测量结果: 距离={:.1f}cm, 形状={}, 尺寸={:.1f}cm".format(distance_cm, shape_type, size_cm))
            return distance_cm, shape_type, size_cm

        except Exception as e:
            print("测量异常: {}".format(e))
            return None

    def _detect_a4_paper(self, img):
        """
        检测A4纸边框

        参数:
            img: 输入图像

        返回:
            dict: A4纸检测结果包含corners, width_px, height_px 或 None
        """
        try:
            if img is None:
                print("A4纸检测失败: 输入图像为空")
                return None

            # K230平台实现
            return self._detect_a4_k230(img)

        except Exception as e:
            print("A4纸检测异常: {}".format(e))
            return None

    def _detect_a4_k230(self, img):
        """
        K230平台的A4纸检测实现 - 方案一：边缘检测 + 轮廓分析
        检测A4纸四边的2cm黑色边框线，获取内部有效区域

        参数:
            img: K230图像对象

        返回:
            dict: 检测结果 或 None
        """
        try:
            print("开始A4纸检测（边缘检测方案）...")

            # 1. 图像预处理 - 灰度化转换
            if img.format() != image.GRAYSCALE:
                gray_img = img.to_grayscale()  # 将彩色图像转为灰度图像
            else:
                gray_img = img  # 已经是灰度图像，直接使用

            # 2. 边缘检测 - 检测黑色边框线，优化阈值参数
            # 使用更敏感的阈值来检测2cm黑色边框
            try:
                # 优化Canny边缘检测阈值：降低低阈值，提高高阈值
                edges = gray_img.find_edges(image.EDGE_CANNY, threshold=(7, 25))#(20, 80)
                print("使用优化的Canny边缘检测 (20, 80)")
            except:
                # 如果Canny不可用，使用简单边缘检测，同样优化阈值
                edges = gray_img.find_edges(threshold=(7, 25))#(20, 80)
                print("使用优化的简单边缘检测 (20, 80)")

            # 3. 查找轮廓 - 检测边框线形成的轮廓
            try:
                # 尝试使用find_contours方法
                contours = edges.find_contours()
                print("检测到{}个轮廓".format(len(contours)))
            except:
                # 如果find_contours不可用，使用find_rects作为备选
                print("find_contours不可用，使用find_rects备选方案")
                return self._detect_a4_fallback(gray_img)

            if not contours:
                print("未检测到轮廓，尝试备选方案")
                return self._detect_a4_fallback(gray_img)

            # 4. 筛选A4纸轮廓 - 增强版本，添加边框宽度验证
            best_contour = None
            best_score = 0

            for contour in contours:
                # 轮廓近似为多边形
                try:
                    # 计算轮廓周长
                    perimeter = contour.perimeter()
                    if perimeter < 150:  # 提高最小周长要求，过滤更小的噪声
                        continue

                    # 轮廓近似，使用更精确的epsilon
                    epsilon = 0.015 * perimeter  # 从2%降低到1.5%，提高精度
                    approx = contour.approx_poly(epsilon)

                    # 检查是否为四边形（A4纸应该是矩形）
                    if len(approx) != 4:
                        continue

                    # 计算轮廓面积
                    area = contour.area()
                    if area < self.min_a4_area or area > self.max_a4_area:
                        continue

                    # 计算边界矩形
                    x, y, w, h = contour.rect()
                    aspect_ratio = max(w, h) / min(w, h)
                    expected_ratio = self.a4_aspect_ratio
                    ratio_diff = abs(aspect_ratio - expected_ratio)

                    if ratio_diff > self.aspect_tolerance:
                        continue

                    # 新增：边框宽度验证 - 确保检测到的是2cm边框
                    border_width_score = self._validate_border_width(w, h)
                    if border_width_score < 0.5:  # 边框宽度不符合要求
                        continue

                    # 新增：轮廓形状质量评估
                    shape_quality = self._evaluate_contour_shape(approx)
                    if shape_quality < 0.6:  # 形状质量不够好
                        continue

                    # 优化得分计算，增加新的评估因子
                    area_score = min(area / self.max_a4_area, 1.0)
                    ratio_score = max(0, 1.0 - ratio_diff / self.aspect_tolerance)
                    contour_score = min(len(approx) / 4.0, 1.0)  # 四边形得分

                    # 综合得分：面积30% + 长宽比30% + 轮廓20% + 边框10% + 形状10%
                    total_score = (area_score * 0.3 + ratio_score * 0.3 +
                                 contour_score * 0.2 + border_width_score * 0.1 +
                                 shape_quality * 0.1)

                    if total_score > best_score:
                        best_score = total_score
                        best_contour = contour

                except Exception as e:
                    print("轮廓处理异常: {}".format(e))
                    continue

            if best_contour is None:
                print("未找到符合A4纸特征的轮廓，尝试多阈值融合策略")
                return self._multi_threshold_fusion_detection(gray_img)

            # 5. 提取A4纸角点坐标
            try:
                # 获取轮廓的边界矩形
                x, y, w, h = best_contour.rect()

                # 计算有效区域（减去2cm边框）
                # 假设边框占总尺寸的比例
                border_ratio_w = 4.0 / self.a4_width_cm  # 左右边框4cm / 总宽度21cm
                border_ratio_h = 4.0 / self.a4_height_cm  # 上下边框4cm / 总高度29.7cm

                border_w = int(w * border_ratio_w / 2)  # 单边边框宽度
                border_h = int(h * border_ratio_h / 2)  # 单边边框高度

                # 有效区域角点（内部白色区域）
                effective_x = x + border_w
                effective_y = y + border_h
                effective_w = w - 2 * border_w
                effective_h = h - 2 * border_h

                corners = [
                    (effective_x, effective_y),                           # 左上角
                    (effective_x + effective_w, effective_y),             # 右上角
                    (effective_x + effective_w, effective_y + effective_h), # 右下角
                    (effective_x, effective_y + effective_h)              # 左下角
                ]

                # 构建检测结果字典
                result = {
                    'corners': corners,                    # 有效区域四个角点坐标
                    'width_px': effective_w,              # 有效区域像素宽度
                    'height_px': effective_h,             # 有效区域像素高度
                    'area': effective_w * effective_h,    # 有效区域像素面积
                    'confidence': best_score,             # 检测置信度
                    'border_detected': True               # 标记使用了边框检测
                }

                print("A4纸检测成功: 总尺寸{}x{}, 有效区域{}x{}, 置信度{:.2f}".format(w, h, effective_w, effective_h, best_score))
                return result

            except Exception as e:
                print("角点提取异常: {}".format(e))
                return None

        except Exception as e:
            print("K230 A4纸检测异常: {}".format(e))
            return None

    def _detect_a4_fallback(self, gray_img):
        """
        A4纸检测备选方案 - 基于二值化和矩形检测
        当边缘检测方法不可用时使用

        参数:
            gray_img: 灰度图像

        返回:
            dict: 检测结果 或 None
        """
        try:
            print("使用A4纸检测备选方案...")

            # 1. 多阈值检测，提高鲁棒性
            best_result = None
            best_confidence = 0

            # 优化阈值范围，增加更多选项以适应不同光照条件
            thresholds = [
                [(0, 50)],      # 检测深黑色边框（强光照）
                [(0, 70)],      # 检测黑色边框（正常光照）
                [(0, 90)],      # 检测较浅黑色边框（弱光照）
                [(0, 110)],     # 检测更浅的边框（很弱光照）
                [(160, 255)],   # 检测亮白色区域（强光照）
                [(180, 255)],   # 检测白色区域（正常光照）
                [(200, 255)]    # 检测很亮的白色区域（过曝光照）
            ]

            for threshold in thresholds:
                try:
                    # 二值化处理
                    binary_img = gray_img.binary(threshold)

                    # 检测矩形 - 优化阈值设置
                    if threshold in [[(160, 255)], [(180, 255)], [(200, 255)]]:  # 白色检测
                        rects = binary_img.find_rects(threshold=self.min_a4_area)
                    else:  # 黑色边框检测，使用更严格的阈值
                        rects = binary_img.find_rects(threshold=max(3000, self.min_a4_area // 3))

                    if not rects:
                        continue

                    # 筛选最佳矩形
                    for rect in rects:
                        area = rect.w() * rect.h()

                        # 面积过滤
                        if area < self.min_a4_area or area > self.max_a4_area:
                            continue

                        # 长宽比过滤
                        aspect_ratio = max(rect.w(), rect.h()) / min(rect.w(), rect.h())
                        ratio_diff = abs(aspect_ratio - self.a4_aspect_ratio)

                        if ratio_diff > self.aspect_tolerance:
                            continue

                        # 优化置信度计算，添加边框验证
                        area_score = min(area / self.max_a4_area, 1.0)
                        ratio_score = max(0, 1.0 - ratio_diff / self.aspect_tolerance)

                        # 添加边框宽度验证
                        border_score = self._validate_border_width(rect.w(), rect.h())

                        # 综合置信度：面积40% + 长宽比40% + 边框20%
                        confidence = area_score * 0.4 + ratio_score * 0.4 + border_score * 0.2

                        if confidence > best_confidence:
                            best_confidence = confidence

                            # 构建结果
                            x, y, w, h = rect.x(), rect.y(), rect.w(), rect.h()

                            # 如果是白色检测，直接使用；如果是黑色检测，需要计算内部区域
                            if threshold in [[(160, 255)], [(180, 255)], [(200, 255)]]:
                                # 白色区域检测，直接使用
                                corners = [
                                    (x, y), (x + w, y), (x + w, y + h), (x, y + h)
                                ]
                                effective_w, effective_h = w, h
                                is_white_detection = True
                            else:
                                # 黑色边框检测，计算内部有效区域
                                border_ratio_w = 4.0 / self.a4_width_cm
                                border_ratio_h = 4.0 / self.a4_height_cm
                                border_w = int(w * border_ratio_w / 2)
                                border_h = int(h * border_ratio_h / 2)

                                effective_x = x + border_w
                                effective_y = y + border_h
                                effective_w = w - 2 * border_w
                                effective_h = h - 2 * border_h

                                corners = [
                                    (effective_x, effective_y),
                                    (effective_x + effective_w, effective_y),
                                    (effective_x + effective_w, effective_y + effective_h),
                                    (effective_x, effective_y + effective_h)
                                ]
                                is_white_detection = False

                            best_result = {
                                'corners': corners,
                                'width_px': effective_w,
                                'height_px': effective_h,
                                'area': effective_w * effective_h,
                                'confidence': confidence,
                                'border_detected': not is_white_detection,
                                'detection_method': 'white' if is_white_detection else 'border'
                            }

                except Exception as e:
                    print(f"阈值{threshold}检测失败: {e}")
                    continue

            if best_result:
                print(f"备选方案检测成功: 尺寸{best_result['width_px']}x{best_result['height_px']}, 置信度{best_confidence:.2f}")
                return best_result
            else:
                print("备选方案也未能检测到A4纸")
                return None

        except Exception as e:
            print("备选方案异常: {}".format(e))
            return None

    def _validate_border_width(self, detected_w, detected_h):
        """
        验证检测到的矩形是否符合2cm边框的A4纸特征

        参数:
            detected_w: 检测到的宽度(像素)
            detected_h: 检测到的高度(像素)

        返回:
            float: 边框宽度验证得分 (0-1)
        """
        try:
            # 计算期望的边框像素宽度
            # 假设检测到的是完整A4纸（包含边框）
            expected_border_w = detected_w * (4.0 / self.a4_width_cm)  # 左右边框总共4cm
            expected_border_h = detected_h * (4.0 / self.a4_height_cm)  # 上下边框总共4cm

            # 边框宽度应该在合理范围内（2cm边框对应的像素范围）
            min_border_w = detected_w * 0.15  # 最小15%（约3.15cm）
            max_border_w = detected_w * 0.25  # 最大25%（约5.25cm）
            min_border_h = detected_h * 0.10  # 最小10%（约2.97cm）
            max_border_h = detected_h * 0.20  # 最大20%（约5.94cm）

            # 计算边框宽度得分
            w_score = 1.0 if min_border_w <= expected_border_w <= max_border_w else 0.5
            h_score = 1.0 if min_border_h <= expected_border_h <= max_border_h else 0.5

            # 综合得分
            border_score = (w_score + h_score) / 2.0

            print(f"边框验证: 期望边框{expected_border_w:.1f}x{expected_border_h:.1f}, 得分{border_score:.2f}")
            return border_score

        except Exception as e:
            print("边框宽度验证异常: {}".format(e))
            return 0.5  # 默认中等得分

    def _evaluate_contour_shape(self, approx_points):
        """
        评估轮廓形状质量，检查是否接近矩形

        参数:
            approx_points: 轮廓近似后的角点列表

        返回:
            float: 形状质量得分 (0-1)
        """
        try:
            if len(approx_points) != 4:
                return 0.0

            # 计算四个角的角度，理想矩形应该都接近90度
            angles = []
            for i in range(4):
                p1 = approx_points[i]
                p2 = approx_points[(i + 1) % 4]
                p3 = approx_points[(i + 2) % 4]

                # 计算向量
                v1 = (p1[0] - p2[0], p1[1] - p2[1])
                v2 = (p3[0] - p2[0], p3[1] - p2[1])

                # 计算角度
                import math
                dot_product = v1[0] * v2[0] + v1[1] * v2[1]
                mag1 = math.sqrt(v1[0]**2 + v1[1]**2)
                mag2 = math.sqrt(v2[0]**2 + v2[1]**2)

                if mag1 > 0 and mag2 > 0:
                    cos_angle = dot_product / (mag1 * mag2)
                    cos_angle = max(-1, min(1, cos_angle))  # 限制范围
                    angle = math.acos(cos_angle) * 180 / math.pi
                    angles.append(angle)

            if len(angles) == 4:
                # 计算角度偏离90度的程度
                angle_deviations = [abs(angle - 90) for angle in angles]
                avg_deviation = sum(angle_deviations) / 4

                # 角度得分：偏离越小得分越高
                angle_score = max(0, 1.0 - avg_deviation / 30.0)  # 30度内给分

                print(f"形状质量: 角度{angles}, 平均偏离{avg_deviation:.1f}度, 得分{angle_score:.2f}")
                return angle_score
            else:
                return 0.5

        except Exception as e:
            print("轮廓形状评估异常: {}".format(e))
            return 0.5  # 默认中等得分

    def _multi_threshold_fusion_detection(self, gray_img):
        """
        多阈值融合检测策略 - 结合边缘检测和二值化的优势

        参数:
            gray_img: 灰度图像

        返回:
            dict: 检测结果 或 None
        """
        try:
            print("使用多阈值融合检测策略...")

            # 收集所有可能的检测结果
            all_candidates = []

            # 1. 尝试不同的边缘检测阈值
            edge_thresholds = [(15, 75), (20, 80), (25, 85), (30, 90)]
            for low, high in edge_thresholds:
                try:
                    edges = gray_img.find_edges(image.EDGE_CANNY, threshold=(low, high))
                    contours = edges.find_contours()

                    for contour in contours:
                        result = self._evaluate_single_contour(contour, "edge_{}_{}".format(low, high))
                        if result:
                            all_candidates.append(result)
                except:
                    continue

            # 2. 尝试二值化检测（调用优化后的备选方案）
            fallback_result = self._detect_a4_fallback(gray_img)
            if fallback_result:
                fallback_result['detection_method'] = 'binary_fallback'
                all_candidates.append(fallback_result)

            # 3. 融合所有候选结果，选择最佳的
            if not all_candidates:
                print("多阈值融合：未找到任何候选结果")
                return None

            # 按置信度排序，选择最佳结果
            all_candidates.sort(key=lambda x: x['confidence'], reverse=True)
            best_result = all_candidates[0]

            print("多阈值融合成功: 方法={}, 置信度={:.3f}".format(
                best_result.get('detection_method', 'unknown'),
                best_result['confidence']))

            return best_result

        except Exception as e:
            print("多阈值融合检测异常: {}".format(e))
            return None

    def _evaluate_single_contour(self, contour, method_name):
        """
        评估单个轮廓是否符合A4纸特征

        参数:
            contour: 轮廓对象
            method_name: 检测方法名称

        返回:
            dict: 检测结果 或 None
        """
        try:
            # 基本几何检查
            perimeter = contour.perimeter()
            if perimeter < 150:
                return None

            area = contour.area()
            if area < self.min_a4_area or area > self.max_a4_area:
                return None

            # 轮廓近似
            epsilon = 0.015 * perimeter
            approx = contour.approx_poly(epsilon)
            if len(approx) != 4:
                return None

            # 长宽比检查
            x, y, w, h = contour.rect()
            aspect_ratio = max(w, h) / min(w, h)
            ratio_diff = abs(aspect_ratio - self.a4_aspect_ratio)
            if ratio_diff > self.aspect_tolerance:
                return None

            # 计算综合得分
            area_score = min(area / self.max_a4_area, 1.0)
            ratio_score = max(0, 1.0 - ratio_diff / self.aspect_tolerance)
            border_score = self._validate_border_width(w, h)
            shape_score = self._evaluate_contour_shape(approx)

            confidence = (area_score * 0.3 + ratio_score * 0.3 +
                         border_score * 0.2 + shape_score * 0.2)

            # 构建结果
            border_ratio_w = 4.0 / self.a4_width_cm
            border_ratio_h = 4.0 / self.a4_height_cm
            border_w = int(w * border_ratio_w / 2)
            border_h = int(h * border_ratio_h / 2)

            effective_x = x + border_w
            effective_y = y + border_h
            effective_w = w - 2 * border_w
            effective_h = h - 2 * border_h

            corners = [
                (effective_x, effective_y),
                (effective_x + effective_w, effective_y),
                (effective_x + effective_w, effective_y + effective_h),
                (effective_x, effective_y + effective_h)
            ]

            return {
                'corners': corners,
                'width_px': effective_w,
                'height_px': effective_h,
                'area': effective_w * effective_h,
                'confidence': confidence,
                'border_detected': True,
                'detection_method': method_name
            }

        except Exception as e:
            print(f"轮廓评估异常 ({method_name}): {e}")
            return None


    def _correct_perspective(self, img, corners):
        """
        透视变换矫正，将A4纸从任意角度矫正为标准顶视图

        参数:
            img: 输入图像
            corners: A4纸四个角点坐标列表 [(x1,y1), (x2,y2), (x3,y3), (x4,y4)]

        返回:
            矫正后的图像对象 或 None
        """
        try:
            if img is None or corners is None or len(corners) != 4:
                print("透视矫正失败: 输入参数无效")
                return None

            print("开始透视变换矫正...")

            # K230平台实现
            return self._correct_perspective_k230(img, corners)

        except Exception as e:
            print(f"透视变换异常: {e}")
            return None

    def _correct_perspective_k230(self, img, corners):
        """
        K230平台的透视变换实现

        参数:
            img: K230图像对象
            corners: 四个角点坐标列表

        返回:
            矫正后的图像对象 或 None
        """
        try:
            # 1. 按顺序排列角点：左上、右上、右下、左下
            sorted_corners = self._sort_corners(corners)
            if sorted_corners is None:
                print("角点排序失败")
                return None

            tl, tr, br, bl = sorted_corners  # 左上、右上、右下、左下
            print(f"排序后角点: 左上{tl}, 右上{tr}, 右下{br}, 左下{bl}")

            # 2. 计算目标矩形尺寸(保持A4比例)
            # 计算原始A4纸的宽度和高度
            width1 = self._calculate_distance(tl, tr)  # 上边长度
            width2 = self._calculate_distance(bl, br)  # 下边长度
            height1 = self._calculate_distance(tl, bl) # 左边长度
            height2 = self._calculate_distance(tr, br) # 右边长度

            # 取平均值作为目标尺寸
            avg_width = int((width1 + width2) / 2)
            avg_height = int((height1 + height2) / 2)

            # 确保保持A4纸有效区域比例
            target_width = avg_width
            target_height = int(target_width * self.a4_aspect_ratio)

            # 如果高度超出合理范围，以高度为准重新计算宽度
            if target_height > avg_height * 1.5:
                target_height = avg_height
                target_width = int(target_height / self.a4_aspect_ratio)

            print(f"目标矩形尺寸: {target_width}x{target_height}")

            # 3. 定义目标四个角点坐标
            dst_corners = [
                (0, 0),                           # 左上角
                (target_width - 1, 0),            # 右上角
                (target_width - 1, target_height - 1),  # 右下角
                (0, target_height - 1)            # 左下角
            ]

            # 4. 在K230上使用简化的透视变换
            # 由于K230可能没有完整的透视变换API，使用仿射变换近似
            warped_img = self._apply_perspective_transform_k230(img, sorted_corners, dst_corners, target_width, target_height)

            if warped_img is not None:
                print(f"透视矫正成功: 输出尺寸{target_width}x{target_height}")
                return warped_img
            else:
                print("透视变换应用失败")
                return None

        except Exception as e:
            print(f"K230透视变换异常: {e}")
            return None



    def _sort_corners(self, corners):
        """
        将四个角点按左上、右上、右下、左下的顺序排列

        参数:
            corners: 四个角点坐标列表 [(x1,y1), (x2,y2), (x3,y3), (x4,y4)]

        返回:
            排序后的角点元组 (左上, 右上, 右下, 左下) 或 None
        """
        try:
            if len(corners) != 4:
                return None

            # 将角点转换为列表便于计算
            pts = corners.copy()

            # 计算质心（中心点）
            center_x = sum(pt[0] for pt in pts) / 4  # 所有x坐标的平均值
            center_y = sum(pt[1] for pt in pts) / 4  # 所有y坐标的平均值

            # 根据相对于质心的位置对角点进行分类
            top_left = None     # 左上角：x < center_x 且 y < center_y
            top_right = None    # 右上角：x > center_x 且 y < center_y
            bottom_right = None # 右下角：x > center_x 且 y > center_y
            bottom_left = None  # 左下角：x < center_x 且 y > center_y

            for pt in pts:
                x, y = pt
                if x < center_x and y < center_y:
                    top_left = pt
                elif x > center_x and y < center_y:
                    top_right = pt
                elif x > center_x and y > center_y:
                    bottom_right = pt
                elif x < center_x and y > center_y:
                    bottom_left = pt

            # 检查是否所有角点都找到了
            if None in [top_left, top_right, bottom_right, bottom_left]:
                print("角点分类失败，使用备用排序方法")
                return self._sort_corners_fallback(corners)

            return (top_left, top_right, bottom_right, bottom_left)

        except Exception as e:
            print(f"角点排序异常: {e}")
            return None

    def _sort_corners_fallback(self, corners):
        """
        备用角点排序方法，基于距离排序

        参数:
            corners: 四个角点坐标列表

        返回:
            排序后的角点元组 或 None
        """
        try:
            # 按y坐标排序，分为上下两组
            sorted_by_y = sorted(corners, key=lambda pt: pt[1])
            top_two = sorted_by_y[:2]    # y坐标较小的两个点（上方）
            bottom_two = sorted_by_y[2:] # y坐标较大的两个点（下方）

            # 在上方两点中，按x坐标排序得到左上和右上
            top_sorted = sorted(top_two, key=lambda pt: pt[0])
            top_left, top_right = top_sorted[0], top_sorted[1]

            # 在下方两点中，按x坐标排序得到左下和右下
            bottom_sorted = sorted(bottom_two, key=lambda pt: pt[0])
            bottom_left, bottom_right = bottom_sorted[0], bottom_sorted[1]

            return (top_left, top_right, bottom_right, bottom_left)

        except Exception as e:
            print(f"备用角点排序异常: {e}")
            return None

    def _calculate_distance(self, pt1, pt2):
        """
        计算两点之间的欧几里得距离

        参数:
            pt1: 第一个点坐标 (x1, y1)
            pt2: 第二个点坐标 (x2, y2)

        返回:
            两点间距离（像素）
        """
        try:
            x1, y1 = pt1
            x2, y2 = pt2
            # 欧几里得距离公式: sqrt((x2-x1)² + (y2-y1)²)
            distance = math.sqrt((x2 - x1) ** 2 + (y2 - y1) ** 2)
            return distance
        except Exception as e:
            print(f"距离计算异常: {e}")
            return 0

    def _apply_perspective_transform_k230(self, img, src_corners, dst_corners, target_width, target_height):
        """
        在K230平台上应用透视变换

        参数:
            img: 输入图像
            src_corners: 源角点坐标列表
            dst_corners: 目标角点坐标列表
            target_width: 目标图像宽度
            target_height: 目标图像高度

        返回:
            变换后的图像 或 None
        """
        try:
            print("应用K230透视变换...")

            # K230简化实现：使用ROI裁剪代替透视变换
            # 注意：dst_corners参数在当前简化实现中未使用，因为使用ROI裁剪而非真正的透视变换
            # 计算源矩形的边界框
            tl, tr, br, bl = src_corners

            # 计算边界框
            min_x = min(tl[0], tr[0], br[0], bl[0])
            max_x = max(tl[0], tr[0], br[0], bl[0])
            min_y = min(tl[1], tr[1], br[1], bl[1])
            max_y = max(tl[1], tr[1], br[1], bl[1])

            # 确保坐标在图像范围内
            min_x = max(0, int(min_x))
            min_y = max(0, int(min_y))
            max_x = min(img.width() - 1, int(max_x))
            max_y = min(img.height() - 1, int(max_y))

            # 计算ROI尺寸
            roi_width = max_x - min_x
            roi_height = max_y - min_y

            if roi_width <= 0 or roi_height <= 0:
                print("ROI尺寸无效")
                return None

            print(f"ROI区域: ({min_x}, {min_y}, {roi_width}, {roi_height})")

            # 使用copy()方法裁剪ROI区域
            try:
                # 方法1: 使用copy裁剪
                warped_img = img.copy(roi=(min_x, min_y, roi_width, roi_height))

                # 如果需要调整到目标尺寸，可以使用scale
                if warped_img.width() != target_width or warped_img.height() != target_height:
                    # 计算缩放比例
                    scale_x = target_width / warped_img.width()
                    scale_y = target_height / warped_img.height()
                    # 使用较小的缩放比例保持比例
                    scale = min(scale_x, scale_y)

                    new_width = int(warped_img.width() * scale)
                    new_height = int(warped_img.height() * scale)

                    if new_width > 0 and new_height > 0:
                        warped_img = warped_img.scale(x_scale=scale, y_scale=scale)

                print(f"透视变换完成: 输出尺寸{warped_img.width()}x{warped_img.height()}")
                return warped_img

            except Exception as e:
                print(f"ROI裁剪失败: {e}")
                # 备用方案：直接返回原图像的缩放版本
                try:
                    scale_x = target_width / img.width()
                    scale_y = target_height / img.height()
                    scale = min(scale_x, scale_y, 1.0)  # 不放大，只缩小

                    if scale < 1.0:
                        warped_img = img.scale(x_scale=scale, y_scale=scale)
                    else:
                        warped_img = img.copy()

                    print(f"使用备用方案: 输出尺寸{warped_img.width()}x{warped_img.height()}")
                    return warped_img

                except Exception as e2:
                    print(f"备用方案也失败: {e2}")
                    return None

        except Exception as e:
            print(f"K230透视变换应用异常: {e}")
            return None

    def _detect_shape(self, warped_img):
        """
        形状识别和尺寸计算

        参数:
            warped_img: 矫正后的A4纸图像

        返回:
            tuple: (shape_type, size_px) 或 None
                - shape_type: str, 形状类型 ('circle', 'triangle', 'square')
                - size_px: float, 图形尺寸(像素) - 圆形为直径，其他为边长
        """
        try:
            if warped_img is None:
                print("形状识别失败: 输入图像为空")
                return None

            print("开始形状识别...")

            # K230平台实现
            return self._detect_shape_k230(warped_img)

        except Exception as e:
            print("形状识别异常: {}".format(e))
            return None

    def _detect_shape_k230(self, warped_img):
        """
        K230平台的形状识别实现

        参数:
            warped_img: K230矫正后的图像对象

        返回:
            tuple: (shape_type, size_px) 或 None
        """
        try:
            print("使用K230形状识别...")

            # 1. 在矫正图像中使用find_blobs()检测黑色区域
            # 设置黑色阈值范围，检测黑色图形 - 使用类属性中的阈值参数
            black_threshold = [(0, self.BLACK_L_MAX, self.BLACK_A_MIN, self.BLACK_A_MAX, self.BLACK_B_MIN, self.BLACK_B_MAX)]
            blobs = warped_img.find_blobs(black_threshold,
                                        pixels_threshold=self.PIXELS_THRESHOLD,
                                        area_threshold=self.AREA_THRESHOLD)

            if not blobs:
                print("未检测到黑色图形区域")
                return None

            # 2. 过滤面积过小的噪声区域，选择最大的blob
            largest_blob = None
            max_area = 0

            for blob in blobs:
                blob_area = blob.pixels()  # blob的像素数量
                if blob_area > max_area and blob_area > self.MIN_BLOB_AREA:  # 过滤小噪声
                    max_area = blob_area
                    largest_blob = blob

            if largest_blob is None:
                print("未找到足够大的图形区域")
                return None

            print("检测到图形区域，面积: {}像素".format(max_area))

            # 3. 对blob进行轮廓分析
            shape_analysis = self._analyze_blob_shape_k230(largest_blob)
            if shape_analysis is None:
                print("形状分析失败")
                return None

            shape_type, size_px = shape_analysis
            print("K230形状识别结果: {}, 尺寸: {:.1f}像素".format(shape_type, size_px))
            return shape_type, size_px

        except Exception as e:
            print("K230形状识别异常: {}".format(e))
            return None



    def _analyze_blob_shape_k230(self, blob):
        """
        K230平台的blob形状分析

        参数:
            blob: K230检测到的blob对象

        返回:
            tuple: (shape_type, size_px) 或 None
        """
        try:
            # 获取blob的基本属性
            blob_area = blob.pixels()           # blob面积(像素数)
            blob_perimeter = blob.perimeter()   # blob周长(像素)
            blob_rect = blob.rect()            # blob的边界矩形 (x, y, w, h)

            # 计算几何特征
            _, _, rect_w, rect_h = blob_rect
            aspect_ratio = max(rect_w, rect_h) / min(rect_w, rect_h)  # 长宽比

            # 计算圆形度: 4π × 面积 / 周长²
            if blob_perimeter > 0:
                circularity = (4 * math.pi * blob_area) / (blob_perimeter ** 2)
            else:
                circularity = 0

            # 计算矩形度: blob面积 / 边界矩形面积
            rect_area = rect_w * rect_h
            if rect_area > 0:
                rectangularity = blob_area / rect_area
            else:
                rectangularity = 0

            print("形状特征: 圆形度={:.3f}, 长宽比={:.3f}, 矩形度={:.3f}".format(circularity, aspect_ratio, rectangularity))

            # 形状判断逻辑 - 使用类属性中的阈值参数
            if circularity > self.CIRCLE_CIRCULARITY_MIN and rectangularity < self.CIRCLE_RECTANGULARITY_MAX:
                # 圆形：高圆形度，中等矩形度
                shape_type = "circle"
                # 计算等效直径: 2 × sqrt(面积/π)
                diameter_px = 2 * math.sqrt(blob_area / math.pi)
                size_px = diameter_px
                print("识别为圆形，直径: {:.1f}像素".format(size_px))

            elif (rectangularity > self.SQUARE_RECTANGULARITY_MIN and
                  aspect_ratio < self.SQUARE_ASPECT_RATIO_MAX and
                  circularity > self.SQUARE_CIRCULARITY_MIN):
                # 正方形：高矩形度，长宽比接近1，中等圆形度
                shape_type = "square"
                # 边长使用几何平均，更准确
                side_length_px = math.sqrt(rect_w * rect_h)
                size_px = side_length_px
                print("识别为正方形，边长: {:.1f}像素".format(size_px))

            elif (circularity < self.TRIANGLE_CIRCULARITY_MAX and
                  rectangularity < self.TRIANGLE_RECTANGULARITY_MAX):
                # 三角形：低圆形度，低矩形度
                shape_type = "triangle"
                # 计算等效边长: sqrt(4×面积/√3)
                side_length_px = math.sqrt(4 * blob_area / math.sqrt(3))
                size_px = side_length_px
                print("识别为三角形，边长: {:.1f}像素".format(size_px))

            else:
                # 无法明确分类，根据最接近的特征判断
                print("形状特征模糊，使用备用判断逻辑")
                if rectangularity > circularity:
                    # 矩形度更高，倾向于正方形
                    shape_type = "square"
                    side_length_px = math.sqrt(rect_w * rect_h)
                    size_px = side_length_px
                    print("备用逻辑识别为正方形，边长: {:.1f}像素".format(size_px))
                elif circularity > self.FALLBACK_CIRCLE_THRESHOLD:
                    # 圆形度很高，倾向于圆形
                    shape_type = "circle"
                    diameter_px = 2 * math.sqrt(blob_area / math.pi)
                    size_px = diameter_px
                    print("备用逻辑识别为圆形，直径: {:.1f}像素".format(size_px))
                else:
                    # 其他情况默认为三角形
                    shape_type = "triangle"
                    side_length_px = math.sqrt(4 * blob_area / math.sqrt(3))
                    size_px = side_length_px
                    print("备用逻辑识别为三角形，边长: {:.1f}像素".format(size_px))

            return shape_type, size_px

        except Exception as e:
            print("blob形状分析异常: {}".format(e))
            return None

    def _calculate_shape_features(self, contour_points):
        """
        计算轮廓的几何特征

        参数:
            contour_points: 轮廓点列表

        返回:
            dict: 几何特征字典
        """
        try:
            if len(contour_points) < 3:
                return None

            # 计算轮廓面积（使用鞋带公式）
            area = 0
            n = len(contour_points)
            for i in range(n):
                j = (i + 1) % n
                area += contour_points[i][0] * contour_points[j][1]
                area -= contour_points[j][0] * contour_points[i][1]
            area = abs(area) / 2

            # 计算轮廓周长
            perimeter = 0
            for i in range(n):
                j = (i + 1) % n
                dx = contour_points[j][0] - contour_points[i][0]
                dy = contour_points[j][1] - contour_points[i][1]
                perimeter += math.sqrt(dx*dx + dy*dy)

            # 计算边界框
            x_coords = [pt[0] for pt in contour_points]
            y_coords = [pt[1] for pt in contour_points]
            min_x, max_x = min(x_coords), max(x_coords)
            min_y, max_y = min(y_coords), max(y_coords)
            bbox_width = max_x - min_x
            bbox_height = max_y - min_y

            return {
                'area': area,
                'perimeter': perimeter,
                'bbox_width': bbox_width,
                'bbox_height': bbox_height,
                'aspect_ratio': max(bbox_width, bbox_height) / min(bbox_width, bbox_height),
                'circularity': (4 * math.pi * area) / (perimeter ** 2) if perimeter > 0 else 0,
                'rectangularity': area / (bbox_width * bbox_height) if bbox_width * bbox_height > 0 else 0
            }

        except Exception as e:
            print("几何特征计算异常: {}".format(e))
            return None

    def get_calibration_status(self):
        """
        获取当前标定状态信息

        返回:
            dict: 包含标定状态和焦距的字典
                - calibrated: bool, 是否已完成标定
                - focal_length: float, 摄像头等效焦距值
        """
        return {
            'calibrated': self.calibrated,      # 标定完成标志
            'focal_length': self.focal_length   # 计算得到的焦距值
        }

    def get_last_measurement(self):
        """
        获取最后一次测量结果的副本

        返回:
            dict: 测量结果字典的副本
                - distance_cm: float, 测量距离(厘米)
                - shape_type: str, 图形类型
                - size_cm: float, 图形尺寸(厘米)
                - timestamp: int, 测量时间戳(毫秒)
        """
        return self.last_measurement.copy()  # 返回副本避免外部修改

    def reset_calibration(self):
        """
        重置标定状态，清除焦距数据
        用于重新标定或切换测量环境时调用
        """
        self.focal_length = None    # 清除焦距数据
        self.calibrated = False     # 重置标定状态标志
        print("标定状态已重置")

    def set_detection_params(self, min_area=None, max_area=None, aspect_tolerance=None):
        """
        动态设置A4纸检测算法参数

        参数:
            min_area: int, A4纸最小面积阈值(像素²)，用于过滤小噪声
            max_area: int, A4纸最大面积阈值(像素²)，用于过滤过大区域
            aspect_tolerance: float, 长宽比容差范围，允许的偏差值
        """
        # 仅更新非None的参数，保持其他参数不变
        if min_area is not None:
            self.min_a4_area = min_area         # 更新最小面积阈值
        if max_area is not None:
            self.max_a4_area = max_area         # 更新最大面积阈值
        if aspect_tolerance is not None:
            self.aspect_tolerance = aspect_tolerance  # 更新长宽比容差

        print("检测参数已更新: 面积范围[{}, {}], 容差={}".format(self.min_a4_area, self.max_a4_area, self.aspect_tolerance))



